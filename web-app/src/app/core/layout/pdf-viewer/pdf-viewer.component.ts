import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  template: `
    <div class="pdf-container">
      @if (pdfUrl) {
        <embed [src]="pdfUrl" type="application/pdf" class="h-full w-full" />
      } @else {
        <div class="flex items-center justify-center h-full">
          <p class="text-gray-500">No PDF URL provided</p>
        </div>
      }
    </div>
  `,
})
export class PdfViewerComponent {
  protected pdfUrl: string | null;

  constructor(private readonly route: ActivatedRoute) {
    this.pdfUrl = this.route.snapshot.queryParams['url'] || null;
  }
}
