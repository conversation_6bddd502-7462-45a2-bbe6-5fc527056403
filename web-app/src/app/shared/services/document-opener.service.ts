import { Injectable, inject } from '@angular/core';
import { Router } from '@angular/router';

import { Observable, map } from 'rxjs';

import { ExportType, IdentificationDocument, IdentificationDocumentsService } from '@digifischdok/ngx-register-sdk';

@Injectable({
  providedIn: 'root',
})
export class DocumentOpenerService {
  private static readonly OBJECT_URL_TIMEOUT = 60 * 1000; // 60 seconds

  private readonly router = inject(Router);

  constructor(private readonly documentsApi: IdentificationDocumentsService) {}

  public openDocument$(registerEntryId: string, document: IdentificationDocument, openInNewTab = true): Observable<void> {
    const exportType: ExportType = this.getExportType(document);

    return this.documentsApi
      .identificationDocumentsControllerExport(registerEntryId, document.documentId, exportType, 'body', false, {
        httpHeaderAccept: 'application/pdf',
      })
      .pipe(
        map((pdf) => {
          const fileUrl = window.URL.createObjectURL(pdf);

          if (openInNewTab) {
            // For new tab, use window.open with the Angular route
            const url = this.router.createUrlTree(['/pdf-viewer'], { queryParams: { url: fileUrl } });
            window.open(window.location.origin + url.toString(), '_blank');
          } else {
            // For same tab, use Angular router navigation
            this.router.navigate(['/pdf-viewer']);
          }

          // Clean up the blob URL after a timeout to prevent memory leaks
          setTimeout(() => {
            window.URL.revokeObjectURL(fileUrl);
          }, DocumentOpenerService.OBJECT_URL_TIMEOUT);
        })
      );
  }

  private getExportType(document: IdentificationDocument): ExportType {
    if (document.tax) {
      return 'FISHING_TAXES';
    }

    if (document.fishingLicense) {
      return 'FISHING_LICENSE';
    }

    if (document.limitedLicenseApproval) {
      return 'LIMITED_LICENSE_APPROVAL';
    }

    throw new Error('Unsupported document type');
  }
}
